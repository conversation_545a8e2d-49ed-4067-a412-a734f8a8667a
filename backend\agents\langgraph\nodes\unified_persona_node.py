"""
Unified Persona Node for LangGraph-based Datagenius System.

This module provides a unified agent node that handles all persona types through 
configuration-driven strategies, eliminating hardcoded persona logic.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from ..states.unified_state import UnifiedDatageniusState
from .base_agent_node import BaseAgentNode
try:
    from ..tools.mcp_integration import MCPToolManager
except ImportError:
    # Fallback to the correct location
    try:
        from ...tools.mcp.agent_integration import MC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    except ImportError:
        # Create a mock MCPToolManager if not available
        class MCPToolManager:
            def __init__(self):
                pass
            async def execute_tool(self, tool_name: str, context: Dict[str, Any]) -> Dict[str, Any]:
                return {"result": "Tool execution not available", "success": False}
from ..intelligence.business_context_integration import BusinessContextManager
from ..intelligence.cross_agent_intelligence import CrossAgentIntelligenceManager
from ..strategies.extensible_strategy_system import ExtensiblePersonaStrategy
from ..config.dynamic_config_loader import dynamic_config_loader

logger = logging.getLogger(__name__)


class UnifiedPersonaNode(BaseAgentNode):
    """
    Unified agent node that handles all persona types through configuration.

    This node eliminates code duplication by using extensible strategy patterns for
    persona-specific behavior while maintaining shared infrastructure.
    All behavior is configuration-driven with no hardcoded values.
    """

    def __init__(
        self,
        persona_config: Dict[str, Any],
        business_profile: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the unified persona node.

        Args:
            persona_config: Configuration for the persona
            business_profile: Optional business profile context
        """
        super().__init__(
            agent_id=persona_config.get("persona_id", "unknown"),
            agent_type=persona_config.get("agent_type", "default"),
            config=persona_config
        )

        # Create persona-specific strategy using extensible system
        self.persona_strategy = self._create_persona_strategy(persona_config)

        # Initialize shared infrastructure
        self.tool_manager = MCPToolManager()
        self.business_context = BusinessContextManager(business_profile)
        self.cross_agent_intelligence = CrossAgentIntelligenceManager()

        # Configuration
        self.persona_config = persona_config
        self.business_profile = business_profile or {}

        # Processing metrics
        self.processing_metrics = {
            "messages_processed": 0,
            "tools_executed": 0,
            "errors_encountered": 0,
            "average_processing_time": 0.0
        }

        self.logger.info(f"Initialized UnifiedPersonaNode for {self.agent_id} ({self.agent_type})")

    def _create_persona_strategy(self, config: Dict[str, Any]) -> ExtensiblePersonaStrategy:
        """
        Create persona-specific behavior strategy using extensible system.
        
        This method now exclusively uses the ConfigurablePersonaStrategy to eliminate
        hardcoded strategy classes and ensure all personas use the configuration-driven approach.

        Args:
            config: Persona configuration

        Returns:
            ExtensiblePersonaStrategy instance (always ConfigurablePersonaStrategy)
        """
        try:
            # Always use ConfigurablePersonaStrategy - no hardcoded fallbacks
            from ..strategies.extensible_strategy_system import ConfigurablePersonaStrategy
            
            self.logger.info(f"Creating ConfigurablePersonaStrategy for {config.get('name', 'unknown')}")
            
            # Create strategy with full configuration
            strategy = ConfigurablePersonaStrategy(config)
            
            self.logger.info(f"Successfully created ConfigurablePersonaStrategy for {self.agent_type}")
            return strategy
            
        except Exception as e:
            self.logger.error(f"Error creating ConfigurablePersonaStrategy: {e}")
            
            # Create minimal fallback strategy with basic configuration
            fallback_config = {
                "name": config.get("name", "Fallback Assistant"),
                "agent_type": config.get("agent_type", "default"),
                "capabilities": config.get("capabilities", ["general_assistance"]),
                "llm_config": config.get("llm_config", {}),
                "prompt_templates": {
                    "system_prompt": "You are a helpful AI assistant.",
                    "default": "I'm here to help! How can I assist you today?"
                },
                "response_templates": {
                    "default": "I'm here to help! How can I assist you today?"
                },
                "processing_rules": {
                    "processing_pipeline": [
                        {
                            "name": "response_generation",
                            "type": "response_generation"
                        }
                    ]
                }
            }
            
            from ..strategies.extensible_strategy_system import ConfigurablePersonaStrategy
            return ConfigurablePersonaStrategy(fallback_config)

    async def __call__(self, state: UnifiedDatageniusState) -> UnifiedDatageniusState:
        """
        Process the current state using the unified persona node.

        Args:
            state: Current workflow state

        Returns:
            Updated workflow state
        """
        start_time = datetime.now()

        try:
            self.logger.info(f"Processing message with {self.agent_type} persona")

            # Prepare processing context
            context = await self._prepare_processing_context(state)

            # Apply business profile context
            context = await self.business_context.enrich_context(context)

            # Apply cross-agent intelligence
            context = await self.cross_agent_intelligence.apply_intelligence(context, state)

            # Process with persona-specific strategy
            updated_state = await self.persona_strategy.process_message(state, context)

            # Execute tools if needed
            if self._should_execute_tools(updated_state, context):
                updated_state = await self._execute_tools(updated_state, context)

            # Update metrics
            self._update_metrics(start_time)

            # Update state with processing metadata
            updated_state.agent_context[self.agent_id] = {
                "last_processed": datetime.now().isoformat(),
                "persona_type": self.agent_type,
                "processing_successful": True
            }

            return updated_state

        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
            self.processing_metrics["errors_encountered"] += 1

            # Return state with error information
            state.agent_context[self.agent_id] = {
                "last_processed": datetime.now().isoformat(),
                "persona_type": self.agent_type,
                "processing_successful": False,
                "error": str(e)
            }

            return state

    async def _prepare_processing_context(self, state: UnifiedDatageniusState) -> Dict[str, Any]:
        """
        Prepare context for processing.

        Args:
            state: Current workflow state

        Returns:
            Processing context
        """
        context = {
            "user_id": state["user_id"],
            "conversation_id": state["conversation_id"],
            "business_profile_id": state.get("business_profile_id"),
            "agent_type": self.agent_type,
            "agent_id": self.agent_id,
            "current_message": state.get("current_message"),
            "conversation_history": state.get("messages", []),
            "workflow_context": state.get("workflow_context", {}),
            "business_context": state.get("business_context", {}),
            "cross_agent_context": state.get("cross_agent_context", {}),
            "available_tools": state.get("available_tools", []),
            "persona_config": self.persona_config
        }

        return context

    def _should_execute_tools(self, state: UnifiedDatageniusState, context: Dict[str, Any]) -> bool:
        """
        Determine if tools should be executed based on state and context.

        Args:
            state: Current workflow state
            context: Processing context

        Returns:
            True if tools should be executed
        """
        # Check if tools are requested in workflow context
        if state.workflow_context.get("tools_requested"):
            return True

        # Check if specific tools are mentioned in the message
        current_message = state.get("current_message", {})
        message_content = current_message.get("content", "").lower()

        tool_keywords = ["analyze", "chart", "graph", "data", "visualization", "report"]
        return any(keyword in message_content for keyword in tool_keywords)

    async def _execute_tools(self, state: UnifiedDatageniusState, context: Dict[str, Any]) -> UnifiedDatageniusState:
        """
        Execute tools based on context and state.

        Args:
            state: Current workflow state
            context: Processing context

        Returns:
            Updated state with tool results
        """
        try:
            # Get specialized tools for this persona
            specialized_tools = self.persona_strategy.get_specialized_tools()

            # Execute relevant tools
            for tool_name in specialized_tools:
                if self._should_execute_tool(tool_name, context):
                    tool_result = await self.tool_manager.execute_tool(tool_name, context)
                    state.tool_results[tool_name] = tool_result

            self.processing_metrics["tools_executed"] += 1

        except Exception as e:
            self.logger.error(f"Error executing tools: {e}")

        return state

    def _should_execute_tool(self, tool_name: str, context: Dict[str, Any]) -> bool:
        """
        Determine if a specific tool should be executed.

        Args:
            tool_name: Name of the tool
            context: Processing context

        Returns:
            True if tool should be executed
        """
        # Simple heuristic - can be made more sophisticated
        message_content = context.get("current_message", {}).get("content", "").lower()
        
        tool_triggers = {
            "data_analyzer": ["analyze", "analysis", "data"],
            "chart_generator": ["chart", "graph", "visualization"],
            "content_generator": ["content", "write", "create"],
            "persona_recommender": ["recommend", "suggest", "persona"]
        }

        triggers = tool_triggers.get(tool_name, [])
        return any(trigger in message_content for trigger in triggers)

    def _update_metrics(self, start_time: datetime) -> None:
        """
        Update processing metrics.

        Args:
            start_time: Processing start time
        """
        processing_time = (datetime.now() - start_time).total_seconds()
        
        self.processing_metrics["messages_processed"] += 1
        
        # Update average processing time
        current_avg = self.processing_metrics["average_processing_time"]
        message_count = self.processing_metrics["messages_processed"]
        
        new_avg = ((current_avg * (message_count - 1)) + processing_time) / message_count
        self.processing_metrics["average_processing_time"] = new_avg

    def get_capability_score(self, request_context: Dict[str, Any]) -> float:
        """
        Calculate capability score for handling a request.

        Args:
            request_context: Context of the request

        Returns:
            Score between 0.0 and 1.0 indicating capability
        """
        return self.persona_strategy.get_capability_score(request_context)

    def get_system_prompt(self, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Get system prompt for this persona.

        Args:
            context: Optional context for prompt generation

        Returns:
            System prompt string
        """
        prompt_context = context or {}
        prompt_context["business_profile"] = self.business_profile

        return self.persona_strategy.get_system_prompt(prompt_context)

    def get_specialized_tools(self) -> List[str]:
        """
        Get specialized tools for this persona.

        Returns:
            List of tool names
        """
        return self.persona_strategy.get_specialized_tools()

    def get_processing_metrics(self) -> Dict[str, Any]:
        """
        Get processing metrics for this node.

        Returns:
            Processing metrics
        """
        return self.processing_metrics.copy()
