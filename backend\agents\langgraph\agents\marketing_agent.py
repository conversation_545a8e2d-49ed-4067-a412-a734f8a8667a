"""
User-Selected Marketing Agent

This module implements a MarketingAgent that inherits from UserSelectedAgent,
providing user-controlled collaboration capabilities while maintaining the
marketing agent's core functionality of strategy development and content creation.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .user_selected_agent import UserSelectedAgent, CollaborationNeed
from ..nodes.unified_persona_node import UnifiedPersonaNode

logger = logging.getLogger(__name__)


class UserSelectedMarketingAgent(UserSelectedAgent):
    """
    Marketing agent that can be directly selected by users.
    
    This agent provides:
    - Marketing strategy development
    - Campaign planning and optimization
    - Content creation and copywriting
    - User-controlled collaboration with specialists
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the user-selected marketing agent.
        
        Args:
            config: Optional configuration dictionary
        """
        # Define marketing capabilities
        capabilities = [
            "marketing_strategy",
            "campaign_planning",
            "content_creation",
            "market_analysis",
            "brand_development",
            "social_media_strategy",
            "advertising_optimization",
            "customer_segmentation"
        ]
        
        super().__init__(
            agent_id="user_selected_marketing",
            capabilities=capabilities,
            config=config
        )
        
        # Initialize the underlying unified persona node for marketing functionality
        marketing_config = {
            "persona_id": "marketing",
            "agent_type": "marketing",
            "name": "Marketing Expert",
            "description": "Strategic marketing assistant for campaign planning and content creation",
            "strategy_id": "marketing",
            "capabilities": capabilities
        }
        
        # Merge with provided config
        if config:
            marketing_config.update(config)
        
        self.unified_node = UnifiedPersonaNode(
            persona_config=marketing_config,
            business_profile=config.get('business_profile') if config else None
        )
        
        # Marketing-specific configuration
        self.target_audiences = config.get('target_audiences', ['B2B', 'B2C', 'Enterprise', 'SMB']) if config else ['B2B', 'B2C', 'Enterprise', 'SMB']
        self.content_types = config.get('content_types', ['blog', 'social', 'email', 'ads', 'landing_page']) if config else ['blog', 'social', 'email', 'ads', 'landing_page']
        self.marketing_channels = config.get('marketing_channels', ['social_media', 'email', 'content', 'paid_ads', 'seo']) if config else ['social_media', 'email', 'content', 'paid_ads', 'seo']
        
        logger.info("UserSelectedMarketingAgent initialized")

    async def handle_with_own_capabilities(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Handle the user message using the marketing agent's own capabilities.
        
        This method provides:
        - Marketing strategy development
        - Campaign planning and optimization
        - Content creation and copywriting
        - Brand development guidance
        
        Args:
            message: The user's message
            context: Additional context information
            
        Returns:
            Dict containing the marketing agent's response and metadata
        """
        try:
            logger.debug("Marketing agent handling message with own capabilities")
            
            # Prepare state for unified node processing
            state = {
                "messages": [{"role": "user", "content": message}],
                "user_id": context.get("user_id") if context else None,
                "conversation_id": context.get("conversation_id") if context else None,
                "business_profile": context.get("business_profile") if context else None,
                "metadata": context.get("metadata", {}) if context else {}
            }
            
            # Add marketing-specific context
            state["marketing_context"] = {
                "target_audiences": self.target_audiences,
                "content_types": self.content_types,
                "marketing_channels": self.marketing_channels
            }
            
            # Process with the unified persona node
            processed_state = await self.unified_node._process_message(state)
            
            # Extract response from processed state
            response_message = ""
            if "messages" in processed_state and processed_state["messages"]:
                # Get the last assistant message
                for msg in reversed(processed_state["messages"]):
                    if msg.get("role") == "assistant":
                        response_message = msg.get("content", "")
                        break
            
            if not response_message:
                response_message = "I'm your Marketing Expert! I can help you develop marketing strategies, plan campaigns, create content, and optimize your marketing efforts. What marketing challenge can I help you with?"
            
            # Add marketing-specific enhancements
            enhanced_response = await self._enhance_marketing_response(message, response_message, context)
            
            return {
                "message": enhanced_response,
                "metadata": {
                    "agent_type": "marketing",
                    "capabilities_used": self._identify_used_capabilities(message),
                    "processing_method": "unified_persona_node",
                    "marketing_focus": self._identify_marketing_focus(message),
                    "timestamp": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error in marketing own capabilities handling: {e}")
            return {
                "message": "I'm your Marketing Expert! I specialize in developing marketing strategies, planning campaigns, creating compelling content, and optimizing your marketing performance. How can I help boost your marketing efforts today?",
                "metadata": {
                    "error": str(e),
                    "fallback_response": True,
                    "timestamp": datetime.now().isoformat()
                }
            }

    async def assess_collaboration_needs(self, message: str, context: Optional[Dict[str, Any]] = None) -> List[CollaborationNeed]:
        """
        Assess whether collaboration with other agents is needed.
        
        The marketing agent determines if it needs help from specialists like
        data analysts for campaign performance analysis, or content classifiers
        for audience segmentation.
        
        Args:
            message: The user's message
            context: Additional context information
            
        Returns:
            List of CollaborationNeed objects
        """
        try:
            collaboration_needs = []
            message_lower = message.lower()
            
            # Check for data analysis needs in marketing
            marketing_data_keywords = [
                "campaign performance", "marketing analytics", "roi analysis",
                "conversion tracking", "marketing metrics", "performance data",
                "campaign results", "marketing dashboard", "attribution analysis"
            ]
            if any(keyword in message_lower for keyword in marketing_data_keywords):
                collaboration_needs.append(CollaborationNeed(
                    specialist_type="Data Analysis Specialist",
                    task_description="analyze marketing campaign performance and metrics",
                    required_capability="data_analysis",
                    priority="normal",
                    context={"analysis_type": "marketing_performance", "user_message": message}
                ))
            
            # Check for visualization needs for marketing data
            marketing_viz_keywords = [
                "marketing dashboard", "campaign visualization", "performance chart",
                "marketing report", "visual campaign results", "marketing infographic"
            ]
            if any(keyword in message_lower for keyword in marketing_viz_keywords):
                collaboration_needs.append(CollaborationNeed(
                    specialist_type="Visualization Specialist",
                    task_description="create marketing performance visualizations and dashboards",
                    required_capability="data_visualization",
                    priority="normal",
                    context={"viz_type": "marketing", "user_message": message}
                ))
            
            # Check for content classification and audience analysis
            audience_analysis_keywords = [
                "audience segmentation", "customer classification", "target audience analysis",
                "persona development", "customer categorization", "audience insights"
            ]
            if any(keyword in message_lower for keyword in audience_analysis_keywords):
                collaboration_needs.append(CollaborationNeed(
                    specialist_type="Classification Specialist",
                    task_description="analyze and classify customer segments and audiences",
                    required_capability="customer_segmentation",
                    priority="normal",
                    context={"classification_type": "audience", "user_message": message}
                ))
            
            # Check for competitive analysis needs
            competitive_keywords = [
                "competitive analysis", "competitor research", "market analysis",
                "industry benchmarking", "competitive intelligence", "market positioning"
            ]
            if any(keyword in message_lower for keyword in competitive_keywords):
                collaboration_needs.append(CollaborationNeed(
                    specialist_type="Research Specialist",
                    task_description="conduct competitive and market research analysis",
                    required_capability="market_research",
                    priority="normal",
                    context={"research_type": "competitive", "user_message": message}
                ))
            
            logger.debug(f"Marketing agent identified {len(collaboration_needs)} collaboration needs")
            return collaboration_needs
            
        except Exception as e:
            logger.error(f"Error assessing collaboration needs: {e}")
            return []

    async def _enhance_marketing_response(self, original_message: str, base_response: str, 
                                        context: Optional[Dict[str, Any]] = None) -> str:
        """
        Enhance the marketing response with additional helpful information.
        
        Args:
            original_message: The user's original message
            base_response: The base response from the unified node
            context: Additional context
            
        Returns:
            Enhanced response string
        """
        try:
            enhanced_response = base_response
            
            # Add channel recommendations for strategy requests
            if self._is_strategy_request(original_message):
                enhanced_response += f"\n\n📢 **Marketing Channels**: I can help you leverage {', '.join(self.marketing_channels)} to reach your audience effectively."
            
            # Add content type suggestions
            if self._is_content_request(original_message):
                enhanced_response += f"\n\n✍️ **Content Types**: I can create {', '.join(self.content_types)} content tailored to your brand and audience."
            
            # Add audience targeting guidance
            if self._is_audience_request(original_message):
                enhanced_response += f"\n\n🎯 **Target Audiences**: I can help you reach {', '.join(self.target_audiences)} audiences with tailored messaging and strategies."
            
            # Add campaign optimization tips
            if self._is_campaign_request(original_message):
                enhanced_response += "\n\n🚀 **Campaign Optimization**: I can help you optimize for awareness, engagement, conversions, or retention based on your goals."
            
            return enhanced_response
            
        except Exception as e:
            logger.error(f"Error enhancing marketing response: {e}")
            return base_response

    def _identify_used_capabilities(self, message: str) -> List[str]:
        """Identify which capabilities were likely used based on the message."""
        used_capabilities = ["marketing_strategy"]  # Always include base capability
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["campaign", "advertising", "promotion"]):
            used_capabilities.append("campaign_planning")
        
        if any(word in message_lower for word in ["content", "copy", "write", "blog", "social"]):
            used_capabilities.append("content_creation")
        
        if any(word in message_lower for word in ["brand", "branding", "identity", "positioning"]):
            used_capabilities.append("brand_development")
        
        if any(word in message_lower for word in ["audience", "segment", "customer", "target"]):
            used_capabilities.append("customer_segmentation")
        
        if any(word in message_lower for word in ["social media", "facebook", "instagram", "twitter", "linkedin"]):
            used_capabilities.append("social_media_strategy")
        
        return used_capabilities

    def _identify_marketing_focus(self, message: str) -> str:
        """Identify the main marketing focus of the message."""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["strategy", "plan", "approach"]):
            return "strategy"
        elif any(word in message_lower for word in ["content", "copy", "write"]):
            return "content"
        elif any(word in message_lower for word in ["campaign", "advertising"]):
            return "campaign"
        elif any(word in message_lower for word in ["brand", "branding"]):
            return "branding"
        elif any(word in message_lower for word in ["social", "social media"]):
            return "social_media"
        else:
            return "general"

    def _is_strategy_request(self, message: str) -> bool:
        """Check if the message is requesting marketing strategy."""
        strategy_keywords = ["strategy", "plan", "approach", "framework", "roadmap"]
        return any(keyword in message.lower() for keyword in strategy_keywords)

    def _is_content_request(self, message: str) -> bool:
        """Check if the message is requesting content creation."""
        content_keywords = ["content", "copy", "write", "create", "blog", "article", "post"]
        return any(keyword in message.lower() for keyword in content_keywords)

    def _is_audience_request(self, message: str) -> bool:
        """Check if the message is about audience or targeting."""
        audience_keywords = ["audience", "target", "customer", "segment", "persona", "demographic"]
        return any(keyword in message.lower() for keyword in audience_keywords)

    def _is_campaign_request(self, message: str) -> bool:
        """Check if the message is about campaigns."""
        campaign_keywords = ["campaign", "advertising", "promotion", "launch", "ads"]
        return any(keyword in message.lower() for keyword in campaign_keywords)

    def get_agent_info(self) -> Dict[str, Any]:
        """Get information about this marketing agent."""
        base_info = super().get_agent_info()
        base_info.update({
            "agent_type": "marketing",
            "specialization": "marketing_strategy_and_content",
            "target_audiences": self.target_audiences,
            "content_types": self.content_types,
            "marketing_channels": self.marketing_channels,
            "supports_campaign_planning": True,
            "supports_content_creation": True,
            "supports_brand_development": True
        })
        return base_info
