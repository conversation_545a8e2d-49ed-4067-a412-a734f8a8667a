"""
User-Selected Concierge Agent

This module implements a ConciergeAgent that inherits from UserSelectedAgent,
providing user-controlled collaboration capabilities while maintaining the
concierge's core functionality of guidance and persona recommendation.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .user_selected_agent import UserSelectedAgent, CollaborationNeed
from ..nodes.unified_persona_node import UnifiedPersonaNode
from ..strategies.extensible_strategy_system import ConfigurablePersonaStrategy

logger = logging.getLogger(__name__)


class UserSelectedConciergeAgent(UserSelectedAgent):
    """
    Concierge agent that can be directly selected by users.
    
    This agent provides:
    - General conversation and guidance
    - Persona recommendations
    - Data assistance coordination
    - User-controlled collaboration with specialists
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the user-selected concierge agent.
        
        Args:
            config: Optional configuration dictionary
        """
        # Define concierge capabilities
        capabilities = [
            "persona_recommendation",
            "intent_analysis", 
            "conversation_management",
            "data_attachment_assistance",
            "workflow_coordination",
            "user_guidance",
            "business_context_awareness"
        ]
        
        super().__init__(
            agent_id="user_selected_concierge",
            capabilities=capabilities,
            config=config
        )
        
        # Initialize the underlying unified persona node for concierge functionality
        concierge_config = {
            "persona_id": "concierge",
            "agent_type": "concierge",
            "name": "Datagenius Concierge",
            "description": "Your knowledgeable guide to Datagenius AI personas",
            "strategy_id": "concierge",
            "capabilities": capabilities
        }
        
        # Merge with provided config
        if config:
            concierge_config.update(config)
        
        self.unified_node = UnifiedPersonaNode(
            persona_config=concierge_config,
            business_profile=config.get('business_profile') if config else None
        )
        
        # Concierge-specific configuration
        self.recommendation_threshold = config.get('recommendation_threshold', 0.7) if config else 0.7
        self.max_recommendations = config.get('max_recommendations', 3) if config else 3
        self.consider_user_history = config.get('consider_user_history', True) if config else True
        
        logger.info("UserSelectedConciergeAgent initialized")

    async def process_message(
        self,
        message: str,
        user_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process a message using the concierge agent's capabilities.

        This method provides the interface expected by UnifiedAgentNode.

        Args:
            message: The user's message
            user_id: User identifier
            conversation_id: Conversation identifier
            context: Additional context information

        Returns:
            Dict containing the agent's response and metadata
        """
        try:
            logger.debug(f"Concierge processing message: {message[:50]}...")

            # Prepare context with user and conversation info
            full_context = context or {}
            full_context.update({
                "user_id": user_id,
                "conversation_id": conversation_id,
                "is_continuing_conversation": len(full_context.get("conversation_history", [])) > 0
            })

            # Use the handle_with_own_capabilities method
            response = await self.handle_with_own_capabilities(message, full_context)

            logger.debug("Concierge successfully processed message")
            return response

        except Exception as e:
            logger.error(f"Error in concierge process_message: {e}", exc_info=True)
            return {
                "message": "Hello! I'm your Datagenius Concierge. I'm here to help you find the right AI persona for your needs and guide you through using our platform. How can I assist you today?",
                "metadata": {
                    "error": str(e),
                    "agent_type": "concierge",
                    "fallback_response": True,
                    "timestamp": datetime.now().isoformat()
                }
            }

    async def handle_with_own_capabilities(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Handle the user message using the concierge's own capabilities.
        
        This method provides:
        - General conversation and guidance
        - Persona recommendations
        - Data assistance coordination
        - Business context awareness
        
        Args:
            message: The user's message
            context: Additional context information
            
        Returns:
            Dict containing the concierge's response and metadata
        """
        try:
            logger.debug("Concierge handling message with own capabilities")
            
            # Prepare state for unified node processing
            state = {
                "messages": [{"role": "user", "content": message}],
                "user_id": context.get("user_id") if context else None,
                "conversation_id": context.get("conversation_id") if context else None,
                "business_profile": context.get("business_profile") if context else None,
                "metadata": context.get("metadata", {}) if context else {}
            }
            
            # Process with the unified persona node
            processed_state = await self.unified_node._process_message(state)
            
            # Extract response from processed state
            response_message = ""
            if "messages" in processed_state and processed_state["messages"]:
                # Get the last assistant message
                for msg in reversed(processed_state["messages"]):
                    if msg.get("role") == "assistant":
                        response_message = msg.get("content", "")
                        break
            
            if not response_message:
                response_message = "Hello! I'm your Datagenius Concierge. How can I help you today?"
            
            # Add concierge-specific enhancements
            enhanced_response = await self._enhance_concierge_response(message, response_message, context)
            
            return {
                "message": enhanced_response,
                "metadata": {
                    "agent_type": "concierge",
                    "capabilities_used": ["conversation_management", "user_guidance"],
                    "processing_method": "unified_persona_node",
                    "timestamp": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error in concierge own capabilities handling: {e}")
            return {
                "message": "I'm here to help! I can assist you with finding the right AI persona for your needs, provide guidance on using Datagenius, and help coordinate your tasks. What would you like to work on today?",
                "metadata": {
                    "error": str(e),
                    "fallback_response": True,
                    "timestamp": datetime.now().isoformat()
                }
            }

    async def assess_collaboration_needs(self, message: str, context: Optional[Dict[str, Any]] = None) -> List[CollaborationNeed]:
        """
        Assess whether collaboration with other agents is needed.
        
        The concierge analyzes the user's message to determine if specialized
        help is needed from other agents like data analysts, marketers, etc.
        
        Args:
            message: The user's message
            context: Additional context information
            
        Returns:
            List of CollaborationNeed objects
        """
        try:
            collaboration_needs = []
            message_lower = message.lower()
            
            # Check for data analysis needs
            data_keywords = [
                "analyze", "analysis", "data", "chart", "graph", "visualization", 
                "statistics", "metrics", "dashboard", "report", "insights"
            ]
            if any(keyword in message_lower for keyword in data_keywords):
                collaboration_needs.append(CollaborationNeed(
                    specialist_type="Data Analysis Specialist",
                    task_description="analyze your data and provide insights",
                    required_capability="data_analysis",
                    priority="normal",
                    context={"analysis_type": "general", "user_message": message}
                ))
            
            # Check for marketing needs
            marketing_keywords = [
                "marketing", "campaign", "content", "social media", "advertising",
                "brand", "promotion", "strategy", "audience", "engagement"
            ]
            if any(keyword in message_lower for keyword in marketing_keywords):
                collaboration_needs.append(CollaborationNeed(
                    specialist_type="Marketing Expert",
                    task_description="develop marketing strategies and content",
                    required_capability="marketing_strategy",
                    priority="normal",
                    context={"marketing_type": "general", "user_message": message}
                ))
            
            # Check for visualization needs
            viz_keywords = [
                "visualize", "chart", "graph", "plot", "dashboard", "infographic",
                "visual", "display", "show me", "create chart"
            ]
            if any(keyword in message_lower for keyword in viz_keywords):
                collaboration_needs.append(CollaborationNeed(
                    specialist_type="Visualization Specialist", 
                    task_description="create visualizations and charts",
                    required_capability="data_visualization",
                    priority="normal",
                    context={"viz_type": "general", "user_message": message}
                ))
            
            # Check for classification needs
            classification_keywords = [
                "classify", "categorize", "organize", "sort", "group", "label",
                "tag", "sentiment", "topic", "category"
            ]
            if any(keyword in message_lower for keyword in classification_keywords):
                collaboration_needs.append(CollaborationNeed(
                    specialist_type="Classification Specialist",
                    task_description="classify and organize your content",
                    required_capability="text_classification",
                    priority="normal",
                    context={"classification_type": "general", "user_message": message}
                ))
            
            logger.debug(f"Concierge identified {len(collaboration_needs)} collaboration needs")
            return collaboration_needs
            
        except Exception as e:
            logger.error(f"Error assessing collaboration needs: {e}")
            return []

    async def _enhance_concierge_response(self, original_message: str, base_response: str, 
                                        context: Optional[Dict[str, Any]] = None) -> str:
        """
        Enhance the concierge response with additional helpful information.
        
        Args:
            original_message: The user's original message
            base_response: The base response from the unified node
            context: Additional context
            
        Returns:
            Enhanced response string
        """
        try:
            enhanced_response = base_response
            
            # Add persona recommendations if appropriate
            if self._should_recommend_personas(original_message):
                persona_info = await self._get_persona_recommendations(original_message, context)
                if persona_info:
                    enhanced_response += f"\n\n{persona_info}"
            
            # Add data assistance offer if data-related
            if self._is_data_related(original_message):
                enhanced_response += "\n\n💡 **Tip**: If you have data files to analyze, I can help you upload and process them with the right AI specialist."
            
            # Add guidance for new users
            if context and context.get("is_new_user"):
                enhanced_response += "\n\n🌟 **New to Datagenius?** I'm here to guide you through our AI personas and help you get the most out of your experience!"
            
            return enhanced_response
            
        except Exception as e:
            logger.error(f"Error enhancing concierge response: {e}")
            return base_response

    def _should_recommend_personas(self, message: str) -> bool:
        """Check if persona recommendations should be included."""
        recommendation_triggers = [
            "what can you do", "help me", "what personas", "which ai", 
            "recommend", "suggest", "best for", "need help with"
        ]
        return any(trigger in message.lower() for trigger in recommendation_triggers)

    def _is_data_related(self, message: str) -> bool:
        """Check if the message is data-related."""
        data_keywords = ["data", "file", "csv", "excel", "analyze", "chart", "report"]
        return any(keyword in message.lower() for keyword in data_keywords)

    async def _get_persona_recommendations(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Get persona recommendations based on the message."""
        try:
            # This would integrate with the persona recommendation system
            # For now, provide a general recommendation
            return (
                "**Available AI Personas:**\n"
                "• **Data Analyst**: Perfect for data analysis, visualization, and insights\n"
                "• **Marketing Expert**: Great for campaigns, content creation, and strategy\n"
                "• **Content Classifier**: Ideal for organizing and categorizing information\n\n"
                "Would you like me to connect you with any of these specialists?"
            )
        except Exception as e:
            logger.error(f"Error getting persona recommendations: {e}")
            return ""

    def get_agent_info(self) -> Dict[str, Any]:
        """Get information about this concierge agent."""
        base_info = super().get_agent_info()
        base_info.update({
            "agent_type": "concierge",
            "specialization": "guidance_and_coordination",
            "recommendation_threshold": self.recommendation_threshold,
            "max_recommendations": self.max_recommendations,
            "supports_persona_recommendation": True,
            "supports_data_assistance": True
        })
        return base_info
