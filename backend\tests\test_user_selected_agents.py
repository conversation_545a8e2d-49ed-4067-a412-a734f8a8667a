"""
Tests for User-Selected Agent System

This module contains comprehensive tests for the user-controlled collaboration
system, including UserSelectedAgent base class and specific agent implementations.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from typing import Dict, List, Optional, Any

from backend.agents.langgraph.agents.user_selected_agent import (
    UserSelectedAgent, 
    CollaborationNeed, 
    CollaborationResult
)
from backend.agents.langgraph.agents.concierge_agent import UserSelectedConciergeAgent
from backend.agents.langgraph.agents.analysis_agent import UserSelectedAnalysisAgent
from backend.agents.langgraph.agents.marketing_agent import UserSelectedMarketingAgent
from backend.agents.langgraph.agents.visualization_agent import UserSelectedVisualizationAgent


class MockUserSelectedAgent(UserSelectedAgent):
    """Mock implementation of UserSelectedAgent for testing."""
    
    def __init__(self, agent_id: str = "test_agent", capabilities: List[str] = None):
        super().__init__(
            agent_id=agent_id,
            capabilities=capabilities or ["test_capability"],
            config={"test_config": True}
        )
        self.handle_calls = []
        self.assess_calls = []
    
    async def handle_with_own_capabilities(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        self.handle_calls.append({"message": message, "context": context})
        return {
            "message": f"Mock response to: {message}",
            "metadata": {"mock": True, "capabilities_used": ["test_capability"]}
        }
    
    async def assess_collaboration_needs(self, message: str, context: Optional[Dict[str, Any]] = None) -> List[CollaborationNeed]:
        self.assess_calls.append({"message": message, "context": context})
        
        # Return collaboration needs based on message content
        needs = []
        if "analyze" in message.lower():
            needs.append(CollaborationNeed(
                specialist_type="Data Analyst",
                task_description="analyze the data",
                required_capability="data_analysis"
            ))
        if "visualize" in message.lower():
            needs.append(CollaborationNeed(
                specialist_type="Visualization Specialist",
                task_description="create visualizations",
                required_capability="data_visualization"
            ))
        
        return needs


class TestUserSelectedAgentBase:
    """Test the UserSelectedAgent base class."""
    
    @pytest.fixture
    def mock_agent(self):
        """Create a mock user-selected agent."""
        return MockUserSelectedAgent()
    
    @pytest.fixture
    def sample_context(self):
        """Sample context for testing."""
        return {
            "user_id": "test_user_123",
            "conversation_id": "conv_456",
            "business_profile": {"industry": "technology"},
            "timestamp": datetime.now().isoformat()
        }
    
    def test_agent_initialization(self, mock_agent):
        """Test agent initialization."""
        assert mock_agent.agent_id == "test_agent"
        assert "test_capability" in mock_agent.capabilities
        assert mock_agent.is_active is True
        assert len(mock_agent.current_conversations) == 0
        assert len(mock_agent.collaboration_history) == 0
    
    @pytest.mark.asyncio
    async def test_process_user_message_basic(self, mock_agent, sample_context):
        """Test basic message processing."""
        message = "Hello, can you help me?"
        user_id = "test_user_123"
        conversation_id = "conv_456"
        
        response = await mock_agent.process_user_message(
            message=message,
            user_id=user_id,
            conversation_id=conversation_id,
            context=sample_context
        )
        
        # Verify response structure
        assert "message" in response
        assert "metadata" in response
        assert response["message"] == f"Mock response to: {message}"
        assert response["metadata"]["agent_id"] == "test_agent"
        
        # Verify conversation tracking
        assert conversation_id in mock_agent.current_conversations
        conv_data = mock_agent.current_conversations[conversation_id]
        assert conv_data["user_id"] == user_id
        assert conv_data["message_count"] == 1
    
    @pytest.mark.asyncio
    async def test_process_user_message_with_collaboration(self, mock_agent, sample_context):
        """Test message processing that triggers collaboration."""
        message = "Please analyze and visualize this data"
        user_id = "test_user_123"
        conversation_id = "conv_456"
        
        # Mock the collaboration handling
        with patch.object(mock_agent, '_handle_collaborations') as mock_collab:
            mock_collab.return_value = [
                CollaborationResult(
                    specialist_id="analyst_1",
                    specialist_type="Data Analyst",
                    result="Analysis complete",
                    success=True
                )
            ]
            
            response = await mock_agent.process_user_message(
                message=message,
                user_id=user_id,
                conversation_id=conversation_id,
                context=sample_context
            )
        
        # Verify collaboration was triggered
        assert len(mock_agent.assess_calls) == 1
        assert mock_collab.called
        
        # Verify response includes collaboration results
        assert "Additional Insights" in response["message"]
        assert response["metadata"]["collaborations_count"] == 1
    
    @pytest.mark.asyncio
    async def test_collaboration_needs_assessment(self, mock_agent):
        """Test collaboration needs assessment."""
        # Test message that should trigger analysis collaboration
        message = "Can you analyze this sales data?"
        needs = await mock_agent.assess_collaboration_needs(message)
        
        assert len(needs) == 1
        assert needs[0].specialist_type == "Data Analyst"
        assert needs[0].required_capability == "data_analysis"
        
        # Test message that should trigger multiple collaborations
        message = "Please analyze and visualize the marketing data"
        needs = await mock_agent.assess_collaboration_needs(message)
        
        assert len(needs) == 2
        specialist_types = [need.specialist_type for need in needs]
        assert "Data Analyst" in specialist_types
        assert "Visualization Specialist" in specialist_types
    
    def test_get_agent_info(self, mock_agent):
        """Test agent info retrieval."""
        info = mock_agent.get_agent_info()
        
        assert info["agent_id"] == "test_agent"
        assert "test_capability" in info["capabilities"]
        assert info["is_active"] is True
        assert info["active_conversations"] == 0
        assert info["total_collaborations"] == 0
    
    @pytest.mark.asyncio
    async def test_conversation_cleanup(self, mock_agent, sample_context):
        """Test conversation cleanup."""
        conversation_id = "conv_to_cleanup"
        
        # Create a conversation
        await mock_agent.process_user_message(
            message="Test message",
            user_id="test_user",
            conversation_id=conversation_id,
            context=sample_context
        )
        
        assert conversation_id in mock_agent.current_conversations
        
        # Clean up conversation
        await mock_agent.cleanup_conversation(conversation_id)
        
        assert conversation_id not in mock_agent.current_conversations


class TestConciergeAgent:
    """Test the UserSelectedConciergeAgent."""
    
    @pytest.fixture
    def concierge_agent(self):
        """Create a concierge agent for testing."""
        return UserSelectedConciergeAgent()
    
    @pytest.mark.asyncio
    async def test_concierge_initialization(self, concierge_agent):
        """Test concierge agent initialization."""
        assert concierge_agent.agent_id == "user_selected_concierge"
        assert "persona_recommendation" in concierge_agent.capabilities
        assert "conversation_management" in concierge_agent.capabilities
        assert concierge_agent.unified_node is not None
    
    @pytest.mark.asyncio
    async def test_concierge_handles_guidance_requests(self, concierge_agent):
        """Test concierge handling guidance requests."""
        message = "What can you help me with?"
        context = {"user_id": "test_user", "conversation_id": "conv_123"}
        
        response = await concierge_agent.handle_with_own_capabilities(message, context)
        
        assert "message" in response
        assert "metadata" in response
        assert response["metadata"]["agent_type"] == "concierge"
    
    @pytest.mark.asyncio
    async def test_concierge_collaboration_assessment(self, concierge_agent):
        """Test concierge collaboration needs assessment."""
        # Test data analysis request
        message = "I need to analyze my sales data and create charts"
        needs = await concierge_agent.assess_collaboration_needs(message)
        
        # Should identify both analysis and visualization needs
        specialist_types = [need.specialist_type for need in needs]
        assert "Data Analysis Specialist" in specialist_types
        assert "Visualization Specialist" in specialist_types


class TestAnalysisAgent:
    """Test the UserSelectedAnalysisAgent."""
    
    @pytest.fixture
    def analysis_agent(self):
        """Create an analysis agent for testing."""
        return UserSelectedAnalysisAgent()
    
    @pytest.mark.asyncio
    async def test_analysis_initialization(self, analysis_agent):
        """Test analysis agent initialization."""
        assert analysis_agent.agent_id == "user_selected_analysis"
        assert "data_analysis" in analysis_agent.capabilities
        assert "statistical_modeling" in analysis_agent.capabilities
        assert "csv" in analysis_agent.supported_formats
    
    @pytest.mark.asyncio
    async def test_analysis_handles_data_requests(self, analysis_agent):
        """Test analysis agent handling data analysis requests."""
        message = "Please analyze this dataset for trends"
        context = {"user_id": "test_user", "has_data": True}
        
        response = await analysis_agent.handle_with_own_capabilities(message, context)
        
        assert "message" in response
        assert response["metadata"]["agent_type"] == "analysis"
        assert "data_analysis" in response["metadata"]["capabilities_used"]
    
    @pytest.mark.asyncio
    async def test_analysis_collaboration_assessment(self, analysis_agent):
        """Test analysis agent collaboration needs assessment."""
        # Test marketing analysis request
        message = "Analyze the performance of our marketing campaigns"
        needs = await analysis_agent.assess_collaboration_needs(message)
        
        # Should identify need for marketing expertise
        specialist_types = [need.specialist_type for need in needs]
        assert "Marketing Expert" in specialist_types


class TestMarketingAgent:
    """Test the UserSelectedMarketingAgent."""
    
    @pytest.fixture
    def marketing_agent(self):
        """Create a marketing agent for testing."""
        return UserSelectedMarketingAgent()
    
    @pytest.mark.asyncio
    async def test_marketing_initialization(self, marketing_agent):
        """Test marketing agent initialization."""
        assert marketing_agent.agent_id == "user_selected_marketing"
        assert "marketing_strategy" in marketing_agent.capabilities
        assert "campaign_planning" in marketing_agent.capabilities
        assert "B2B" in marketing_agent.target_audiences
    
    @pytest.mark.asyncio
    async def test_marketing_handles_strategy_requests(self, marketing_agent):
        """Test marketing agent handling strategy requests."""
        message = "Help me create a marketing strategy for our new product"
        context = {"user_id": "test_user", "business_profile": {"industry": "tech"}}
        
        response = await marketing_agent.handle_with_own_capabilities(message, context)
        
        assert "message" in response
        assert response["metadata"]["agent_type"] == "marketing"
        assert response["metadata"]["marketing_focus"] == "strategy"


class TestVisualizationAgent:
    """Test the UserSelectedVisualizationAgent."""
    
    @pytest.fixture
    def viz_agent(self):
        """Create a visualization agent for testing."""
        return UserSelectedVisualizationAgent()
    
    @pytest.mark.asyncio
    async def test_viz_initialization(self, viz_agent):
        """Test visualization agent initialization."""
        assert viz_agent.agent_id == "user_selected_visualization"
        assert "data_visualization" in viz_agent.capabilities
        assert "chart_creation" in viz_agent.capabilities
        assert "bar" in viz_agent.chart_types
        assert "plotly" in viz_agent.visualization_libraries
    
    @pytest.mark.asyncio
    async def test_viz_handles_chart_requests(self, viz_agent):
        """Test visualization agent handling chart requests."""
        message = "Create a bar chart showing sales by region"
        context = {"user_id": "test_user", "has_data": True}
        
        response = await viz_agent.handle_with_own_capabilities(message, context)
        
        assert "message" in response
        assert response["metadata"]["agent_type"] == "visualization"
        assert response["metadata"]["visualization_type"] == "chart"
    
    @pytest.mark.asyncio
    async def test_viz_collaboration_assessment(self, viz_agent):
        """Test visualization agent collaboration needs assessment."""
        # Test request that needs data preparation
        message = "Create charts from this messy dataset that needs cleaning"
        needs = await viz_agent.assess_collaboration_needs(message)
        
        # Should identify need for data analysis
        specialist_types = [need.specialist_type for need in needs]
        assert "Data Analysis Specialist" in specialist_types


class TestAgentIntegration:
    """Test integration between different agents."""
    
    @pytest.mark.asyncio
    async def test_agent_capability_coverage(self):
        """Test that agents cover different capabilities without overlap."""
        concierge = UserSelectedConciergeAgent()
        analysis = UserSelectedAnalysisAgent()
        marketing = UserSelectedMarketingAgent()
        viz = UserSelectedVisualizationAgent()
        
        all_capabilities = set()
        agents = [concierge, analysis, marketing, viz]
        
        for agent in agents:
            agent_caps = set(agent.capabilities)
            # Check for minimal overlap (some overlap is expected)
            overlap = all_capabilities.intersection(agent_caps)
            assert len(overlap) < len(agent_caps) / 2  # Less than 50% overlap
            all_capabilities.update(agent_caps)
        
        # Verify we have good coverage of different capability types
        assert len(all_capabilities) > 15  # Should have diverse capabilities
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test error handling in agent processing."""
        agent = MockUserSelectedAgent()
        
        # Test with invalid context
        response = await agent.process_user_message(
            message="Test message",
            user_id="test_user",
            conversation_id="conv_123",
            context=None  # No context
        )
        
        # Should still return a valid response
        assert "message" in response
        assert "metadata" in response
        
        # Test with empty message
        response = await agent.process_user_message(
            message="",
            user_id="test_user",
            conversation_id="conv_123"
        )
        
        # Should handle gracefully
        assert "message" in response


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
