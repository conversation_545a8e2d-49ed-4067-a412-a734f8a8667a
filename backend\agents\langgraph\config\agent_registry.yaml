# Unified Persona Registry Configuration
# This file defines all available personas and their configurations

personas:
  concierge:
    agent_type: "concierge"
    name: "Datagenius Concierge"
    description: "General purpose agent for greetings, routing, and persona recommendations"
    agent_class: "agents.langgraph.agents.user_selected_concierge_agent.UserSelectedConciergeAgent"
    capabilities:
      - "persona_recommendation"
      - "intent_analysis"
      - "conversation_management"
      - "data_attachment_assistance"
      - "workflow_coordination"
      - "user_guidance"
      - "business_context_awareness"
    supported_intents:
      - "persona_request"
      - "general_inquiry"
      - "data_attachment"
      - "workflow_coordination"
      - "help_request"
      - "greeting"
    tools:
      - "persona_recommender"
      - "data_attachment_assistant"
      - "context_manager"
      - "conversation_state_manager"
    priority: 1
    fallback: true

# Legacy agent configurations removed - now handled by persona system

  composable-classifier-ai:
    agent_type: "classification"
    name: "Composable Classifier AI"
    description: "Specialized agent for content classification and categorization"
    agent_class: "agents.classification.composable_agent.ComposableClassificationAgent"
    capabilities:
      - "content_classification"
      - "text_categorization"
      - "sentiment_analysis"
      - "topic_modeling"
      - "entity_recognition"
      - "document_processing"
      - "data_labeling"
      - "pattern_matching"
    supported_intents:
      - "classification_request"
      - "categorization"
      - "sentiment_analysis"
      - "topic_extraction"
      - "entity_extraction"
      - "document_analysis"
      - "data_labeling"
    tools:
      - "text_classifier"
      - "sentiment_analyzer"
      - "topic_modeler"
      - "entity_extractor"
      - "document_processor"
      - "pattern_matcher"
    priority: 2

  composable-marketing-ai:
    agent_type: "marketing"
    name: "Composable Marketer"
    description: "Strategic marketing assistant for campaign planning and content creation"
    agent_class: "agents.marketing.composable_agent.ComposableMarketingAgent"
    capabilities:
      - "marketing_strategy"
      - "campaign_planning"
      - "content_creation"
      - "market_analysis"
      - "brand_development"
      - "social_media_strategy"
      - "advertising_optimization"
      - "customer_segmentation"
    supported_intents:
      - "marketing_strategy"
      - "campaign_planning"
      - "content_creation"
      - "market_research"
      - "brand_analysis"
      - "social_media"
      - "advertising"
    tools:
      - "market_analyzer"
      - "content_generator"
      - "campaign_planner"
      - "brand_analyzer"
      - "social_media_manager"
      - "ad_optimizer"
    priority: 2

  composable-analysis-ai:
    agent_type: "analysis"
    name: "Composable Analyst"
    description: "Expert data analyst specializing in insights, visualization, and statistical analysis"
    agent_class: "agents.analysis.composable_agent.ComposableAnalysisAgent"
    capabilities:
      - "data_analysis"
      - "statistical_modeling"
      - "data_visualization"
      - "predictive_analytics"
      - "business_intelligence"
      - "report_generation"
      - "trend_analysis"
      - "performance_metrics"
    supported_intents:
      - "data_analysis"
      - "statistical_analysis"
      - "data_visualization"
      - "predictive_modeling"
      - "business_intelligence"
      - "reporting"
      - "trend_analysis"
    tools:
      - "data_analyzer"
      - "statistical_modeler"
      - "chart_generator"
      - "trend_analyzer"
      - "report_builder"
      - "metrics_calculator"
    priority: 2

  concierge-agent:
    agent_type: "concierge"
    name: "Datagenius Concierge"
    description: "General purpose agent for greetings, routing, and persona recommendations"
    agent_class: "agents.langgraph.agents.user_selected_concierge_agent.UserSelectedConciergeAgent"
    capabilities:
      - "persona_recommendation"
      - "intent_analysis"
      - "conversation_management"
      - "data_attachment_assistance"
      - "workflow_coordination"
      - "user_guidance"
      - "business_context_awareness"
    supported_intents:
      - "persona_request"
      - "general_inquiry"
      - "data_attachment"
      - "workflow_coordination"
      - "help_request"
      - "greeting"
    tools:
      - "persona_recommender"
      - "data_attachment_assistant"
      - "context_manager"
      - "conversation_state_manager"
    priority: 1
    fallback: true

# Default configuration for new agents
default_agent_config:
  agent_type: "general"
  capabilities: []
  supported_intents: ["general_inquiry"]
  tools: []
  priority: 3
  fallback: false
  agent_init_config: {}

# Agent discovery settings
discovery:
  enabled: true
  paths:
    - "agents.langgraph.agents"
    - "agents.custom"
  auto_register: true
  scan_interval: 300  # seconds

# Dynamic loading configuration
dynamic_loading:
  enabled: true
  hot_reload: true
  config_watch: true
  fallback_agents:
    - "concierge"
